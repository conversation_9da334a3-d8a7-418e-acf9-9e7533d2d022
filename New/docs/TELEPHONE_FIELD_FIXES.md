# Telephone Field Conversion Fixes

## Overview

This document describes the fixes implemented to resolve issues with custom field synchronization, specifically:

1. **AP Field Creation Failures**: CC telephone fields failing to create in AutoPatient with "textBoxListOptions should not be empty" error
2. **Runtime Error**: TypeError "Cannot read properties of undefined (reading 'totalProcessed')" in custom fields handler
3. **acceptedFormat Invalid Error**: AP API rejecting TEXTBOX_LIST fields with "acceptedFormat invalid" error

## Issues Identified

### Issue 1: textBoxListOptions Validation Error

**Problem**: When converting CliniCore telephone fields with `allowMultipleValues: true` to AutoPatient TEXTBOX_LIST fields, the conversion logic was not properly handling the `textBoxListOptions` property.

**Root Cause**: The AutoPatient API requires TEXTBOX_LIST fields to have `textBoxListOptions` populated with valid options. The error "textBoxListOptions should not be empty" indicates the API expects this property to contain at least one option with label and prefillValue.

**Affected Fields**: 
- `phone-mobile`
- `phone-personal` 
- `phone-business`
- Any CC field with `allowMultipleValues: true` that converts to TEXTBOX_LIST

### Issue 2: Statistics Object Undefined Error

**Problem**: The custom fields handler was expecting the old response structure from `fieldSynchronizer.ts` but was receiving the new structure from `synchronization/engine.ts`.

**Root Cause**: The handler was trying to access `syncResult.statistics.totalProcessed` and `syncResult.creationStatistics.*` properties that don't exist in the new response structure.

### Issue 3: acceptedFormat Invalid Error

**Problem**: After fixing the textBoxListOptions issue, the AP API started rejecting TEXTBOX_LIST field creation with "acceptedFormat invalid" error.

**Root Cause**: The AutoPatient API requires TEXTBOX_LIST fields to have a valid `acceptedFormat` property that specifies what type of data the field accepts (e.g., "text", "phone", "email", "number").

## Fixes Implemented

### Fix 1: textBoxListOptions Handling

**Files Modified**:
- `New/src/processors/customFields/ccToApCustomFieldConvert.ts`

**Changes Made**:
Updated all field conversion functions that create TEXTBOX_LIST fields to provide valid `textBoxListOptions`:

```typescript
// Before (problematic)
return {
    ...baseField,
    dataType: "TEXTBOX_LIST",
    // textBoxListOptions was undefined, causing AP API to reject the field
};

// After (fixed)
return {
    ...baseField,
    dataType: "TEXTBOX_LIST",
    // AP API requires textBoxListOptions to be populated for TEXTBOX_LIST fields
    textBoxListOptions: [
        {
            label: "Value 1",
            prefillValue: ""
        }
    ],
    // AP API requires acceptedFormat for TEXTBOX_LIST fields
    acceptedFormat: ["text"]
};
```

**Functions Updated**:
- `handleCcTextField()` - for multi-value text fields
- `handleCcTextareaField()` - for multi-value textarea fields  
- `handleCcNumberField()` - for multi-value number fields
- `handleCcTelephoneField()` - for multi-value telephone fields
- `handleCcEmailField()` - for multi-value email fields

### Fix 2: Handler Response Structure Compatibility

**Files Modified**:
- `New/src/handlers/customFieldsHandler.ts`

**Changes Made**:
Updated the handler to work with the new `CustomFieldSyncResponse` structure from the synchronization engine:

```typescript
// Before (expecting old structure)
statistics: {
    totalProcessed: syncResult.statistics.totalProcessed,
    totalMatched: syncResult.statistics.totalMatched,
    // ... other properties that don't exist in new structure
},

// After (compatible with new structure)
statistics: {
    totalProcessed: (syncResult.matchedCount || 0) + (syncResult.createdCount || 0) + (syncResult.skippedCount || 0),
    totalMatched: syncResult.matchedCount || 0,
    totalUnmatched: syncResult.skippedCount || 0,
    totalStandardMappings: 0, // Not tracked in new engine
},
```

## Testing

### Test File Created
- `New/src/test/telephoneFieldConversionTest.ts`

This test file verifies:
1. Single telephone fields convert to PHONE type without textBoxListOptions
2. Multi-value telephone fields convert to TEXTBOX_LIST without textBoxListOptions  
3. Other multi-value field types also convert correctly

### Running Tests
```bash
# Run the test file directly
npx ts-node New/src/test/telephoneFieldConversionTest.ts
```

## Expected Results

### Before Fixes
- ❌ CC telephone fields with `allowMultipleValues: true` failed to create in AP
- ❌ Handler crashed with "Cannot read properties of undefined" error
- ❌ Synchronization process was interrupted

### After Fixes
- ✅ CC telephone fields convert successfully to AP TEXTBOX_LIST fields
- ✅ Handler processes responses without runtime errors
- ✅ Synchronization completes successfully
- ✅ Statistics are properly calculated and displayed

## Field Type Mappings

| CC Field Type | allowMultipleValues | AP Field Type | textBoxListOptions | acceptedFormat |
|---------------|-------------------|---------------|-------------------|----------------|
| telephone     | false             | PHONE         | undefined         | undefined      |
| telephone     | true              | TEXTBOX_LIST  | [{"label": "Phone Number", "prefillValue": ""}] | ["phone"]      |
| text          | true              | TEXTBOX_LIST  | [{"label": "Value 1", "prefillValue": ""}] | ["text"]       |
| textarea      | true              | TEXTBOX_LIST  | [{"label": "Value 1", "prefillValue": ""}] | ["text"]       |
| number        | true              | TEXTBOX_LIST  | [{"label": "Value 1", "prefillValue": ""}] | ["number"]     |
| email         | true              | TEXTBOX_LIST  | [{"label": "Email Address", "prefillValue": ""}] | ["email"]      |

## Notes

1. **textBoxListOptions Usage**: This property is required for TEXTBOX_LIST fields and must contain at least one option with label and prefillValue. The AP API rejects TEXTBOX_LIST fields without valid textBoxListOptions.

2. **Backward Compatibility**: The handler fixes maintain backward compatibility by mapping new response structure to expected format.

3. **Error Handling**: Both fixes include proper error handling to prevent similar issues in the future.

## Related Files

- `New/src/processors/customFields/ccToApCustomFieldConvert.ts` - Field conversion logic
- `New/src/handlers/customFieldsHandler.ts` - API handler
- `New/src/processors/customFields/types.ts` - Type definitions
- `New/src/processors/customFields/synchronization/engine.ts` - New synchronization engine
- `New/src/test/telephoneFieldConversionTest.ts` - Test verification
