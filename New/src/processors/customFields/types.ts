/**
 * Custom Field Synchronization Types
 *
 * Type definitions for bidirectional custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms.
 *
 * @fileoverview Custom field synchronization type definitions
 * @version 1.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, APPostCustomfieldType } from "@/type/APTypes";
import type { GetCC<PERSON>ustomField, PostCCCustomField } from "@/type/CCTypes";
import type { StandardFieldMapping } from "@/config/standardFieldMappings";

/**
 * Platform identifier for synchronization operations
 */
export type Platform = "ap" | "cc";

/**
 * Field matching strategy enumeration
 */
export enum FieldMatchStrategy {
	EXACT = "exact",
	NORMALIZED = "normalized",
	FUZZY = "fuzzy",
}

/**
 * AutoPatient field data types
 */
export type APFieldDataType =
	| "TEXT"
	| "LARGE_TEXT"
	| "NUMERICAL"
	| "PHONE"
	| "MONETORY"
	| "CHECKBOX"
	| "SINGLE_OPTIONS"
	| "MULTIPLE_OPTIONS"
	| "DATE"
	| "RADIO"
	| "EMAIL"
	| "TEXTBOX_LIST"
	| "FILE_UPLOAD";

/**
 * CliniCore field types
 */
export type CCFieldType =
	| "text"
	| "textarea"
	| "select"
	| "boolean"
	| "select-or-custom"
	| "number"
	| "telephone"
	| "email"
	| "date"
	| "medication"
	| "permanent-diagnoses"
	| "patient-has-recommended";

/**
 * Field type mapping configuration from AP to CC
 */
export interface APToCCFieldMapping {
	apType: APFieldDataType;
	ccType: CCFieldType;
	allowMultipleValues?: boolean;
	requiresSpecialHandling?: boolean;
	description: string;
}

/**
 * Field type mapping configuration from CC to AP
 */
export interface CCToAPFieldMapping {
	ccType: CCFieldType;
	allowMultipleValues?: boolean;
	apType: APFieldDataType;
	requiresSpecialHandling?: boolean;
	description: string;
}

/**
 * Field matching result
 */
export interface FieldMatchResult {
	matched: boolean;
	apField?: APGetCustomFieldType;
	ccField?: GetCCCustomField;
	matchType: "exact" | "normalized" | "none";
	conflictReason?: string;
}

/**
 * Field synchronization result
 */
export interface FieldSyncResult {
	success: boolean;
	apFieldId?: string;
	ccFieldId?: number;
	action: "created" | "matched" | "skipped" | "failed";
	error?: string;
	warnings?: string[];
}

/**
 * Value conversion context
 */
export interface ValueConversionContext {
	sourceType: APFieldDataType | CCFieldType;
	targetType: APFieldDataType | CCFieldType;
	sourceValue: unknown;
	fieldConfig?: APGetCustomFieldType | GetCCCustomField;
}

/**
 * Value conversion result
 */
export interface ValueConversionResult {
	success: boolean;
	convertedValue: unknown;
	warnings?: string[];
	error?: string;
}

/**
 * Field normalization result
 */
export interface FieldNormalizationResult {
	normalizedName: string;
	originalName: string;
	transformations: string[];
}

/**
 * Field conflict detection result
 */
export interface FieldConflictResult {
	/** Whether a conflict was detected */
	hasConflict: boolean;
	/** Type of conflict detected */
	conflictType?: "standard_field" | "existing_custom_field" | "blocklist";
	/** Standard field mapping if conflict is with standard field */
	standardMapping?: StandardFieldMapping;
	/** Existing field if conflict is with existing custom field */
	existingField?: APGetCustomFieldType | GetCCCustomField;
	/** Blocklist information if conflict is due to blocklist violation */
	blocklistInfo?: {
		pattern: string;
		reason: string;
	};
}

/**
 * Field creation result
 */
export interface FieldCreationResult {
	/** Whether the field creation was successful */
	success: boolean;
	/** Created field data if successful */
	field?: APGetCustomFieldType | GetCCCustomField;
	/** Error message if creation failed */
	error?: string;
	/** Whether the failure was due to existing field conflict */
	existingFieldConflict?: boolean;
}

/**
 * Field matching result
 */
export interface FieldMatchResult {
	/** Whether a match was found */
	matched: boolean;
	/** AutoPatient field in the match */
	apField?: APGetCustomFieldType;
	/** CliniCore field in the match */
	ccField?: GetCCCustomField;
	/** Type of match found */
	matchType: "exact" | "normalized" | "fuzzy" | "none";
	/** Reason for conflict if no match found */
	conflictReason?: string;
}

/**
 * Custom field synchronization response
 */
export interface CustomFieldSyncResponse {
	/** Whether the synchronization was successful */
	success?: boolean;
	/** Number of field pairs matched */
	matchedCount: number;
	/** Number of fields upserted */
	upsertedCount: number;
	/** Number of new fields created */
	createdCount?: number;
	/** Number of fields skipped due to conflicts */
	skippedCount?: number;
	/** Number of errors encountered */
	errorCount?: number;
	/** Warning messages */
	warnings?: string[];
	/** Error messages */
	errors: string[];
	/** Processing time in milliseconds */
	processingTimeMs?: number;
	/** Unmatched AutoPatient fields */
	unmatchedApFields: APGetCustomFieldType[];
	/** Unmatched CliniCore fields */
	unmatchedCcFields: GetCCCustomField[];
	/** Created CliniCore fields */
	createdCcFields: GetCCCustomField[];
	/** Created AutoPatient fields */
	createdApFields: APGetCustomFieldType[];
	/** Blocked AutoPatient fields */
	blockedApFields: APGetCustomFieldType[];
	/** Standard field mappings */
	standardFieldMappings: StandardFieldMapping[];
	/** Synchronization statistics */
	statistics: {
		totalApFields: number;
		totalCcFields: number;
		totalProcessed: number;
		totalMatched: number;
		totalUnmatched: number;
		totalStandardMappings: number;
	};
	/** Field creation statistics */
	creationStatistics: {
		apFieldsCreatedInCc: number;
		ccFieldsCreatedInAp: number;
		totalCreated: number;
		creationErrors: number;
		creationSkippedDueToStandardFields: number;
		creationBlockedCount: number;
		apFieldsSkippedInCcDueToConfig: number;
		ccFieldsSkippedInApDueToConfig: number;
	};
	/** Creation error messages */
	creationErrors: string[];
}

/**
 * Custom field mapping database record
 * Uses the inferred type from the database schema
 */
export type CustomFieldMapping = typeof import("@/database/schema").customFields.$inferSelect;

/**
 * Custom field insert type for database operations
 */
export type CustomFieldInsert = typeof import("@/database/schema").customFields.$inferInsert;

/**
 * Synchronization statistics
 */
export interface SyncStatistics {
	totalProcessed: number;
	successful: number;
	failed: number;
	skipped: number;
	newlyCreated: number;
	matched: number;
	warnings: number;
	errors: string[];
	processingTimeMs: number;
}



/**
 * Field creation request for AP
 */
export interface APFieldCreationRequest extends APPostCustomfieldType {
	sourceField: GetCCCustomField;
	mappingReason: string;
}

/**
 * Field creation request for CC
 */
export interface CCFieldCreationRequest extends PostCCCustomField {
	sourceField: APGetCustomFieldType;
	mappingReason: string;
}

/**
 * Synchronization options
 */
export interface SyncOptions {
	requestId: string;
	dryRun?: boolean;
	skipExisting?: boolean;
	createMissingFields?: boolean;
	logLevel?: "DEBUG" | "INFO" | "WARN" | "ERROR";
}

/**
 * Patient custom field value synchronization context
 */
export interface PatientFieldSyncContext {
	patientId: string;
	platform: "ap" | "cc";
	requestId: string;
	skipMissingFields?: boolean;
}

/**
 * Field value synchronization result
 */
export interface FieldValueSyncResult {
	fieldName: string;
	success: boolean;
	action: "updated" | "created" | "skipped" | "failed";
	originalValue?: unknown;
	convertedValue?: unknown;
	error?: string;
	warnings?: string[];
}

/**
 * Patient field synchronization summary
 */
export interface PatientSyncSummary {
	patientId: string;
	platform: "ap" | "cc";
	totalFields: number;
	processedFields: number;
	successfulUpdates: number;
	failedUpdates: number;
	skippedFields: number;
	fieldResults: FieldValueSyncResult[];
	processingTimeMs: number;
	requestId: string;
}
