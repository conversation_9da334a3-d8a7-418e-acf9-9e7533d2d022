/**
 * Test for telephone field conversion fixes
 * 
 * This test verifies that CC telephone fields are correctly converted to AP fields
 * without causing textBoxListOptions validation errors.
 */

import { ccToApCustomFieldConvert } from "../processors/customFields/ccToApCustomFieldConvert";
import { convertCcFieldToAp } from "../processors/customFields/cc/fieldConverter";
import type { GetCCCustomField } from "@type";

/**
 * Test CC telephone field conversion to AP PHONE field
 */
function testSingleTelephoneFieldConversion() {
	console.log("\n🧪 Testing single telephone field conversion...");
	
	const ccTelephoneField: GetCCCustomField = {
		id: 1,
		name: "phone-mobile",
		label: "Mobile Phone",
		validation: "{}",
		type: "telephone",
		color: null,
		positions: [],
		allowMultipleValues: false,
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: [],
	};

	// Test both conversion functions
	const resultOld = ccToApCustomFieldConvert(ccTelephoneField);
	const resultNew = convertCcFieldToAp(ccTelephoneField);
	
	console.log("Input CC field:", {
		name: ccTelephoneField.name,
		type: ccTelephoneField.type,
		allowMultipleValues: ccTelephoneField.allowMultipleValues,
	});

	console.log("Output AP field (old converter):", {
		name: resultOld.name,
		dataType: resultOld.dataType,
		textBoxListOptions: resultOld.textBoxListOptions,
		fieldKey: resultOld.fieldKey,
		documentType: resultOld.documentType,
		showInForms: resultOld.showInForms,
		model: resultOld.model,
	});

	console.log("Output AP field (new converter):", {
		name: resultNew.name,
		dataType: resultNew.dataType,
		textBoxListOptions: resultNew.textBoxListOptions,
		fieldKey: resultNew.fieldKey,
		documentType: resultNew.documentType,
		showInForms: resultNew.showInForms,
		model: resultNew.model,
	});

	// Verify conversion (using new converter which is actually used)
	if (resultNew.dataType === "PHONE" && resultNew.textBoxListOptions === undefined) {
		console.log("✅ Single telephone field conversion: PASSED");
	} else {
		console.log("❌ Single telephone field conversion: FAILED");
		console.log("Expected: dataType='PHONE', textBoxListOptions=undefined");
		console.log(`Actual: dataType='${resultNew.dataType}', textBoxListOptions=${JSON.stringify(resultNew.textBoxListOptions)}`);
	}
}

/**
 * Test CC multi-value telephone field conversion to AP TEXTBOX_LIST field
 */
function testMultiTelephoneFieldConversion() {
	console.log("\n🧪 Testing multi-value telephone field conversion...");
	
	const ccMultiTelephoneField: GetCCCustomField = {
		id: 2,
		name: "phone-business",
		label: "Business Phones",
		validation: "{}",
		type: "telephone",
		color: null,
		positions: [],
		allowMultipleValues: true,
		useCustomSort: null,
		isRequired: false,
		allowedValues: [],
		defaultValues: [],
	};

	// Test both conversion functions
	const resultOld = ccToApCustomFieldConvert(ccMultiTelephoneField);
	const resultNew = convertCcFieldToAp(ccMultiTelephoneField);
	
	console.log("Input CC field:", {
		name: ccMultiTelephoneField.name,
		type: ccMultiTelephoneField.type,
		allowMultipleValues: ccMultiTelephoneField.allowMultipleValues,
	});
	
	console.log("Output AP field (old converter):", {
		name: resultOld.name,
		dataType: resultOld.dataType,
		textBoxListOptions: resultOld.textBoxListOptions,
		fieldKey: resultOld.fieldKey,
		documentType: resultOld.documentType,
		showInForms: resultOld.showInForms,
		model: resultOld.model,
	});

	console.log("Output AP field (new converter):", {
		name: resultNew.name,
		dataType: resultNew.dataType,
		textBoxListOptions: resultNew.textBoxListOptions,
		fieldKey: resultNew.fieldKey,
		documentType: resultNew.documentType,
		showInForms: resultNew.showInForms,
		model: resultNew.model,
	});

	// Verify conversion (using new converter which is actually used)
	if (resultNew.dataType === "TEXTBOX_LIST" &&
		Array.isArray(resultNew.textBoxListOptions) && resultNew.textBoxListOptions.length > 0 &&
		resultNew.fieldKey === "" && resultNew.documentType === "field" &&
		resultNew.showInForms === true && resultNew.model === "contact") {
		console.log("✅ Multi-value telephone field conversion: PASSED");
	} else {
		console.log("❌ Multi-value telephone field conversion: FAILED");
		console.log("Expected: dataType='TEXTBOX_LIST', textBoxListOptions=[{...}], fieldKey='', documentType='field', showInForms=true, model='contact'");
		console.log(`Actual: dataType='${resultNew.dataType}', textBoxListOptions=${JSON.stringify(resultNew.textBoxListOptions)}, fieldKey='${resultNew.fieldKey}', documentType='${resultNew.documentType}', showInForms=${resultNew.showInForms}, model='${resultNew.model}'`);
	}
}

/**
 * Test other multi-value field types to ensure they also don't set empty textBoxListOptions
 */
function testOtherMultiValueFieldConversions() {
	console.log("\n🧪 Testing other multi-value field conversions...");
	
	const testFields: GetCCCustomField[] = [
		{
			id: 3,
			name: "multi-text",
			label: "Multi Text",
			validation: "{}",
			type: "text",
			color: null,
			positions: [],
			allowMultipleValues: true,
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: [],
		},
		{
			id: 4,
			name: "multi-email",
			label: "Multi Email",
			validation: "{}",
			type: "email",
			color: null,
			positions: [],
			allowMultipleValues: true,
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: [],
		},
		{
			id: 5,
			name: "multi-number",
			label: "Multi Number",
			validation: "{}",
			type: "number",
			color: null,
			positions: [],
			allowMultipleValues: true,
			useCustomSort: null,
			isRequired: false,
			allowedValues: [],
			defaultValues: [],
		},
	];

	let allPassed = true;
	
	for (const ccField of testFields) {
		const resultOld = ccToApCustomFieldConvert(ccField);
		const resultNew = convertCcFieldToAp(ccField);

		console.log(`Testing ${ccField.type} (multi) → ${resultNew.dataType}`);
		console.log(`  Old converter: textBoxListOptions=${JSON.stringify(resultOld.textBoxListOptions)}`);
		console.log(`  New converter: textBoxListOptions=${JSON.stringify(resultNew.textBoxListOptions)}, fieldKey='${resultNew.fieldKey}', documentType='${resultNew.documentType}', showInForms=${resultNew.showInForms}, model='${resultNew.model}'`);

		if (resultNew.dataType === "TEXTBOX_LIST" &&
			Array.isArray(resultNew.textBoxListOptions) && resultNew.textBoxListOptions.length > 0 &&
			resultNew.fieldKey === "" && resultNew.documentType === "field" &&
			resultNew.showInForms === true && resultNew.model === "contact") {
			console.log(`  ✅ ${ccField.type} conversion: PASSED`);
		} else {
			console.log(`  ❌ ${ccField.type} conversion: FAILED`);
			console.log(`  Expected: dataType='TEXTBOX_LIST', textBoxListOptions=[{...}], fieldKey='', documentType='field', showInForms=true, model='contact'`);
			console.log(`  Actual: dataType='${resultNew.dataType}', textBoxListOptions=${JSON.stringify(resultNew.textBoxListOptions)}, fieldKey='${resultNew.fieldKey}', documentType='${resultNew.documentType}', showInForms=${resultNew.showInForms}, model='${resultNew.model}'`);
			allPassed = false;
		}
	}
	
	if (allPassed) {
		console.log("✅ All multi-value field conversions: PASSED");
	} else {
		console.log("❌ Some multi-value field conversions: FAILED");
	}
}

/**
 * Run all tests
 */
export function runTelephoneFieldConversionTests() {
	console.log("🚀 Running telephone field conversion tests...");
	console.log("=" .repeat(60));
	
	testSingleTelephoneFieldConversion();
	testMultiTelephoneFieldConversion();
	testOtherMultiValueFieldConversions();
	
	console.log("\n" + "=".repeat(60));
	console.log("📋 Test Summary:");
	console.log("These tests verify that CC telephone fields convert correctly");
	console.log("without setting empty textBoxListOptions arrays that cause AP API errors.");
	console.log("The fix ensures textBoxListOptions is undefined rather than [].");
}

// Run tests if this file is executed directly
if (require.main === module) {
	runTelephoneFieldConversionTests();
}
